# jpa config
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.url=jdbc:mysql://${database.address}/${database.name}?useUnicode=true&characterEncoding=UTF-8&autoReconnect=true&failOverReadOnly=false&useSSL=false&allowPublicKeyRetrieval=true&useTimezone=true&serverTimezone=Asia/Shanghai&allowMultiQueries=true&rewriteBatchedStatements=true
spring.datasource.username=${database.username}
spring.datasource.password=${database.password}
spring.datasource.type=com.zaxxer.hikari.HikariDataSource
spring.datasource.hikari.read-only=false
spring.datasource.hikari.auto-commit=true
spring.datasource.hikari.minimum-idle=5
spring.datasource.hikari.maximum-pool-size=50
spring.datasource.hikari.idle-timeout=30000
spring.datasource.hikari.pool-name=HikariCP
spring.datasource.hikari.max-lifetime=60000
spring.datasource.hikari.connection-timeout=30000
spring.datasource.hikari.connection-test-query=SELECT 1

spring.data.jpa.repositories.enabled=true
spring.jpa.hibernate.ddl-auto=validate
spring.jpa.generate-ddl=false
spring.jpa.database=MYSQL
spring.jpa.show-sql=false
spring.jpa.open-in-view=false
spring.jpa.properties.hibernate.jdbc.batch_size=10000
spring.jpa.database-platform=org.hibernate.dialect.MySQL5InnoDBDialect

# dubbo config
dubbo.application.name=next-compute-scheduler
dubbo.application.serialize-check-status=WARN
dubbo.application.qos-enable=false
dubbo.protocol.name=dubbo
dubbo.protocol.port=-1
dubbo.registry.protocol=zookeeper
dubbo.registry.address=zookeeper://${zookeeper.address}
dubbo.provider.timeout=${rpc.timeout}
dubbo.provider.retries=0
dubbo.provider.group=${environment.group}
dubbo.provider.filter=RequestProviderFilter

# scheduling
spring.task.scheduling.pool.size=${task.scheduling.pool.size}
spring.task.scheduling.shutdown.await-termination=true
spring.task.scheduling.shutdown.await-termination-period=60
spring.task.scheduling.thread-name-prefix=listener-compute-
spring.scheduler.polling.milliseconds=${scheduler.polling.milliseconds}
spring.scheduler.polling.resend-messages=30000
spring.scheduler.polling.clear-milliseconds=300000
spring.cdc.scheduler.polling.milliseconds=${cdc.scheduler.polling.milliseconds}
spring.scheduler.polling.yarn.resource=300000

# kafka config
spring.kafka.bootstrap-servers=${kafka.bootstrapServers}
spring.kafka.listener.missing-topics-fatal=false
spring.kafka.listener.concurrency=${kafka.listener.concurrency}
spring.kafka.listener.ack-mode=RECORD
spring.kafka.listener.type=SINGLE
spring.kafka.listener.poll-timeout=5000
spring.kafka.producer.retries=0
spring.kafka.producer.acks=1
spring.kafka.producer.batch-size=${kafka.producer.batchSize}
spring.kafka.producer.properties.linger.ms=${kafka.producer.lingerMs}
spring.kafka.producer.buffer-memory=${kafka.producer.bufferMemory}
spring.kafka.producer.key-serializer=org.apache.kafka.common.serialization.ByteArraySerializer
spring.kafka.producer.value-serializer=org.apache.kafka.common.serialization.ByteArraySerializer
spring.kafka.properties.max.request.size=104857600
spring.kafka.computeResultTopic=${kafka.computeResultTopic}
spring.kafka.consumer.properties.group.id=${kafka.consumer.consumeGroup}
spring.kafka.consumer.enable-auto-commit=true
spring.kafka.consumer.auto-commit-interval=${kafka.consumer.autoCommitInterval}
spring.kafka.consumer.auto-offset-reset=${kafka.consumer.autoOffsetReset}
spring.kafka.consumer.properties.session.timeout.ms=120000
spring.kafka.consumer.properties.request.timeout.ms=180000
spring.kafka.consumer.key-deserializer=org.apache.kafka.common.serialization.ByteArrayDeserializer
spring.kafka.consumer.value-deserializer=org.apache.kafka.common.serialization.ByteArrayDeserializer
spring.kafka.consumer.max-poll-records=${kafka.consumer.maxPollRecords}
spring.kafka.residentComputeResultTopic=${kafka.residentComputeResultTopic}

# estimate
spring.estimate.polling.milliseconds=${estimate.polling.milliseconds}
